package org.flamesmplugin;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages all flame-related data and operations
 */
public class FlameManager {
    private final FlameSMPlugin plugin;
    private final Map<UUID, FlamePlayer> flamePlayers;
    private File dataFile;
    private FileConfiguration dataConfig;
    
    public FlameManager(FlameSMPlugin plugin) {
        this.plugin = plugin;
        this.flamePlayers = new HashMap<>();
        setupDataFile();
        loadData();
    }
    
    private void setupDataFile() {
        dataFile = new File(plugin.getDataFolder(), "playerdata.yml");
        if (!dataFile.exists()) {
            plugin.getDataFolder().mkdirs();
            try {
                dataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("Could not create playerdata.yml file!");
                e.printStackTrace();
            }
        }
        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
    }
    
    /**
     * Get or create a FlamePlayer for the given player
     */
    public FlamePlayer getFlamePlayer(Player player) {
        return getFlamePlayer(player.getUniqueId());
    }
    
    public FlamePlayer getFlamePlayer(UUID playerId) {
        return flamePlayers.computeIfAbsent(playerId, FlamePlayer::new);
    }
    
    /**
     * Assign a random flame to a new player
     */
    public void assignRandomFlame(Player player) {
        FlamePlayer flamePlayer = getFlamePlayer(player);
        if (flamePlayer.getFlameType() == null) {
            FlameType randomFlame = FlameType.getRandomFlame();
            flamePlayer.setFlameType(randomFlame);
            player.sendMessage("§6You have been blessed with the " + randomFlame.getDisplayName() + "!");
            savePlayerData(flamePlayer);
        }
    }
    
    /**
     * Handle player death - damage their flame
     */
    public void handlePlayerDeath(Player player) {
        FlamePlayer flamePlayer = getFlamePlayer(player);
        if (flamePlayer.hasActiveFlame()) {
            flamePlayer.addDeath();
            
            if (flamePlayer.isFlameExtinguished()) {
                player.sendMessage("§c§lYour flame has been extinguished! Find a match to relight it.");
                Bukkit.broadcastMessage("§c" + player.getName() + "'s flame has been extinguished!");
            } else {
                int health = flamePlayer.getFlameHealth();
                player.sendMessage("§e§lYour flame has been damaged! Flame health: " + health + "/3");
            }
            
            savePlayerData(flamePlayer);
        }
    }
    
    /**
     * Upgrade a player's flame to level 2
     */
    public void upgradeFlame(Player player) {
        FlamePlayer flamePlayer = getFlamePlayer(player);
        if (flamePlayer.hasActiveFlame() && flamePlayer.getUpgradeLevel() < 2) {
            flamePlayer.setUpgradeLevel(2);
            player.sendMessage("§a§lYour flame has been upgraded! You can now use Level 2 abilities!");
            savePlayerData(flamePlayer);
        }
    }
    
    /**
     * Relight an extinguished flame using a match
     */
    public void relightFlame(Player player) {
        FlamePlayer flamePlayer = getFlamePlayer(player);
        if (flamePlayer.getFlameType() != null && flamePlayer.isFlameExtinguished()) {
            flamePlayer.relightFlame();
            player.sendMessage("§a§lYour flame has been relit! Welcome back, flame bearer!");
            savePlayerData(flamePlayer);
        }
    }
    
    /**
     * Save player data to file
     */
    public void savePlayerData(FlamePlayer flamePlayer) {
        String path = "players." + flamePlayer.getPlayerId().toString();
        if (flamePlayer.getFlameType() != null) {
            dataConfig.set(path + ".flame", flamePlayer.getFlameType().name());
            dataConfig.set(path + ".upgrade", flamePlayer.getUpgradeLevel());
            dataConfig.set(path + ".deaths", flamePlayer.getDeaths());
            dataConfig.set(path + ".extinguished", flamePlayer.isFlameExtinguished());
        }
        saveData();
    }
    
    /**
     * Load all player data from file
     */
    public void loadData() {
        if (dataConfig.getConfigurationSection("players") == null) return;
        
        for (String uuidString : dataConfig.getConfigurationSection("players").getKeys(false)) {
            try {
                UUID playerId = UUID.fromString(uuidString);
                String path = "players." + uuidString;
                
                String flameName = dataConfig.getString(path + ".flame");
                if (flameName != null) {
                    FlameType flameType = FlameType.valueOf(flameName);
                    int upgrade = dataConfig.getInt(path + ".upgrade", 1);
                    int deaths = dataConfig.getInt(path + ".deaths", 0);
                    boolean extinguished = dataConfig.getBoolean(path + ".extinguished", false);
                    
                    FlamePlayer flamePlayer = new FlamePlayer(playerId, flameType, upgrade, deaths, extinguished);
                    flamePlayers.put(playerId, flamePlayer);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to load data for player: " + uuidString);
            }
        }
    }
    
    /**
     * Save data to file
     */
    public void saveData() {
        try {
            dataConfig.save(dataFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Could not save playerdata.yml!");
            e.printStackTrace();
        }
    }
    
    /**
     * Save all player data
     */
    public void saveAllData() {
        for (FlamePlayer flamePlayer : flamePlayers.values()) {
            savePlayerData(flamePlayer);
        }
    }
}
