package org.flamesmplugin;

import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.Arrays;
import java.util.List;

/**
 * Handles creation and management of custom items
 */
public class FlameItems {
    private final FlameSMPlugin plugin;
    private final NamespacedKey customItemKey;
    private final NamespacedKey flameMatchKey;
    private final NamespacedKey flameMaceKey;
    
    public FlameItems(FlameSMPlugin plugin) {
        this.plugin = plugin;
        this.customItemKey = new NamespacedKey(plugin, "custom_item");
        this.flameMatchKey = new NamespacedKey(plugin, "flame_match");
        this.flameMaceKey = new NamespacedKey(plugin, "flame_mace");
    }
    
    /**
     * Create a Flame Match item
     */
    public ItemStack createFlameMatch() {
        ItemStack match = new ItemStack(Material.TORCH);
        ItemMeta meta = match.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName("§6§lFlame Match");
            meta.setLore(Arrays.asList(
                "§7A mystical match that can relight",
                "§7an extinguished flame.",
                "",
                "§e§lRight-click to use"
            ));
            
            // Add custom data to identify this item
            meta.getPersistentDataContainer().set(customItemKey, PersistentDataType.STRING, "flame_match");
            meta.getPersistentDataContainer().set(flameMatchKey, PersistentDataType.BOOLEAN, true);
            
            // Add enchantment glow
            meta.addEnchant(Enchantment.UNBREAKING, 1, true);
            
            match.setItemMeta(meta);
        }
        
        return match;
    }
    
    /**
     * Create a Custom Flame Mace
     */
    public ItemStack createFlameMace() {
        ItemStack mace = new ItemStack(Material.MACE);
        ItemMeta meta = mace.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName("§c§lFlame Mace");
            meta.setLore(Arrays.asList(
                "§7A legendary weapon forged in the",
                "§7flames of ancient dragons.",
                "",
                "§c§lSpecial Abilities:",
                "§7• Dash ability",
                "§7• Breaks all shields within 30 blocks",
                "",
                "§e§lRight-click to dash"
            ));
            
            // Add custom data
            meta.getPersistentDataContainer().set(customItemKey, PersistentDataType.STRING, "flame_mace");
            meta.getPersistentDataContainer().set(flameMaceKey, PersistentDataType.BOOLEAN, true);
            
            // Add enchantments
            meta.addEnchant(Enchantment.SHARPNESS, 5, true);
            meta.addEnchant(Enchantment.UNBREAKING, 3, true);
            meta.addEnchant(Enchantment.FIRE_ASPECT, 2, true);
            
            mace.setItemMeta(meta);
        }
        
        return mace;
    }
    
    /**
     * Create a Flame Upgrader item
     */
    public ItemStack createFlameUpgrader() {
        ItemStack upgrader = new ItemStack(Material.BLAZE_POWDER);
        ItemMeta meta = upgrader.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName("§a§lFlame Upgrader");
            meta.setLore(Arrays.asList(
                "§7Upgrades your flame to Level 2,",
                "§7unlocking powerful new abilities.",
                "",
                "§e§lRight-click to use"
            ));
            
            meta.getPersistentDataContainer().set(customItemKey, PersistentDataType.STRING, "flame_upgrader");
            meta.addEnchant(Enchantment.UNBREAKING, 1, true);
            
            upgrader.setItemMeta(meta);
        }
        
        return upgrader;
    }
    
    /**
     * Check if an item is a Flame Match
     */
    public boolean isFlameMatch(ItemStack item) {
        if (item == null || item.getItemMeta() == null) return false;
        return item.getItemMeta().getPersistentDataContainer()
            .has(flameMatchKey, PersistentDataType.BOOLEAN);
    }
    
    /**
     * Check if an item is a Flame Mace
     */
    public boolean isFlameMace(ItemStack item) {
        if (item == null || item.getItemMeta() == null) return false;
        return item.getItemMeta().getPersistentDataContainer()
            .has(flameMaceKey, PersistentDataType.BOOLEAN);
    }
    
    /**
     * Check if an item is a Flame Upgrader
     */
    public boolean isFlameUpgrader(ItemStack item) {
        if (item == null || item.getItemMeta() == null) return false;
        String customType = item.getItemMeta().getPersistentDataContainer()
            .get(customItemKey, PersistentDataType.STRING);
        return "flame_upgrader".equals(customType);
    }
    
    /**
     * Get the custom item type
     */
    public String getCustomItemType(ItemStack item) {
        if (item == null || item.getItemMeta() == null) return null;
        return item.getItemMeta().getPersistentDataContainer()
            .get(customItemKey, PersistentDataType.STRING);
    }
    
    /**
     * Auto-enchant tools based on flame type
     */
    public void autoEnchantTool(ItemStack item, FlameType flameType) {
        if (item == null || item.getItemMeta() == null) return;
        
        ItemMeta meta = item.getItemMeta();
        
        switch (flameType) {
            case EARTH:
                if (isPickaxe(item)) {
                    meta.addEnchant(Enchantment.EFFICIENCY, 2, true);
                }
                break;
            case AQUATIC:
                if (isHelmet(item)) {
                    meta.addEnchant(Enchantment.AQUA_AFFINITY, 1, true);
                }
                break;
            case COSMIC:
                if (isArmor(item)) {
                    meta.addEnchant(Enchantment.PROTECTION, 1, true);
                }
                break;
        }
        
        item.setItemMeta(meta);
    }
    
    private boolean isPickaxe(ItemStack item) {
        Material type = item.getType();
        return type == Material.WOODEN_PICKAXE || type == Material.STONE_PICKAXE ||
               type == Material.IRON_PICKAXE || type == Material.GOLDEN_PICKAXE ||
               type == Material.DIAMOND_PICKAXE || type == Material.NETHERITE_PICKAXE;
    }
    
    private boolean isHelmet(ItemStack item) {
        Material type = item.getType();
        return type == Material.LEATHER_HELMET || type == Material.CHAINMAIL_HELMET ||
               type == Material.IRON_HELMET || type == Material.GOLDEN_HELMET ||
               type == Material.DIAMOND_HELMET || type == Material.NETHERITE_HELMET ||
               type == Material.TURTLE_HELMET;
    }
    
    private boolean isArmor(ItemStack item) {
        String typeName = item.getType().name();
        return typeName.contains("HELMET") || typeName.contains("CHESTPLATE") ||
               typeName.contains("LEGGINGS") || typeName.contains("BOOTS");
    }
}
