package org.flamesmplugin;

import org.bukkit.*;
import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeInstance;
import org.bukkit.block.Block;
import org.bukkit.entity.*;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import java.util.Random;

/**
 * Handles all flame abilities - both Level 1 (passive) and Level 2 (active)
 */
public class FlameAbilities {
    private final FlameSMPlugin plugin;
    private final FlameManager flameManager;
    private final CooldownManager cooldownManager;
    private final Random random;
    
    public FlameAbilities(FlameSMPlugin plugin, FlameManager flameManager, CooldownManager cooldownManager) {
        this.plugin = plugin;
        this.flameManager = flameManager;
        this.cooldownManager = cooldownManager;
        this.random = new Random();
    }
    
    /**
     * Apply Level 1 passive abilities to a player
     */
    public void applyPassiveAbilities(Player player) {
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(player);
        if (!flamePlayer.hasActiveFlame()) return;
        
        FlameType flameType = flamePlayer.getFlameType();
        
        switch (flameType) {
            case BURNING:
                applyBurningPassive(player);
                break;
            case FROST:
                applyFrostPassive(player);
                break;
            case EARTH:
                applyEarthPassive(player);
                break;
            case AQUATIC:
                applyAquaticPassive(player);
                break;
            case GUST:
                applyGustPassive(player);
                break;
            case COSMIC:
                applyCosmicPassive(player);
                break;
            case SHADOW:
                applyShadowPassive(player);
                break;
            case LIFE:
                applyLifePassive(player);
                break;
            case DRAGON:
                applyDragonPassive(player);
                break;
        }
        
        // Create ambient particles
        if (random.nextInt(20) == 0) { // 5% chance each tick
            ParticleUtils.createAmbientEffect(player, flameType);
        }
    }
    
    /**
     * Use Level 2 active ability
     */
    public boolean useActiveAbility(Player player) {
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(player);
        if (!flamePlayer.canUseLevel2Abilities()) {
            player.sendMessage("§cYou need to upgrade your flame to Level 2 to use this ability!");
            return false;
        }
        
        if (cooldownManager.isOnCooldown(player)) {
            int remaining = cooldownManager.getRemainingCooldown(player);
            player.sendMessage("§cAbility on cooldown! " + remaining + " seconds remaining.");
            return false;
        }
        
        FlameType flameType = flamePlayer.getFlameType();
        boolean success = false;
        
        switch (flameType) {
            case BURNING:
                success = useBurningActive(player);
                break;
            case FROST:
                success = useFrostActive(player);
                break;
            case EARTH:
                success = useEarthActive(player);
                break;
            case AQUATIC:
                success = useAquaticActive(player);
                break;
            case GUST:
                success = useGustActive(player);
                break;
            case COSMIC:
                success = useCosmicActive(player);
                break;
            case SHADOW:
                success = useShadowActive(player);
                break;
            case LIFE:
                success = useLifeActive(player);
                break;
            case DRAGON:
                success = useDragonActive(player);
                break;
        }
        
        if (success) {
            int cooldown = CooldownManager.getCooldownForFlame(flameType);
            if (cooldown > 0) {
                cooldownManager.setCooldown(player, cooldown);
            }
        }
        
        return success;
    }
    
    // ===== LEVEL 1 PASSIVE ABILITIES =====
    
    private void applyBurningPassive(Player player) {
        // Permanent fire resistance 1, strength 1 while on fire
        player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 100, 0, true, false));
        
        if (player.getFireTicks() > 0) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, 100, 0, true, false));
        }
    }
    
    private void applyFrostPassive(Player player) {
        // Immune to freezing, regeneration 2 while in snow
        player.setFreezeTicks(0);
        
        Block blockAt = player.getLocation().getBlock();
        if (blockAt.getType() == Material.SNOW || blockAt.getType() == Material.SNOW_BLOCK ||
            blockAt.getType() == Material.POWDER_SNOW) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 100, 1, true, false));
        }
    }
    
    private void applyEarthPassive(Player player) {
        // Permanent haste 1
        player.addPotionEffect(new PotionEffect(PotionEffectType.HASTE, 100, 0, true, false));
    }
    
    private void applyAquaticPassive(Player player) {
        // Turtle helmet breathing duration, dolphins grace 1
        if (player.isInWater()) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.DOLPHINS_GRACE, 100, 0, true, false));
            // Extend air supply
            if (player.getRemainingAir() < player.getMaximumAir()) {
                player.setRemainingAir(Math.min(player.getMaximumAir(), player.getRemainingAir() + 20));
            }
        }
    }
    
    private void applyGustPassive(Player player) {
        // Permanent speed 1, extra hearts above Y70, immune to fall damage
        player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 0, true, false));
        
        // Extra hearts based on height above Y70
        double y = player.getLocation().getY();
        if (y > 70) {
            int extraHearts = (int) Math.floor((y - 70) / 10) / 2; // Half heart per 10 blocks
            extraHearts = Math.min(extraHearts, 10); // Cap at 5 extra hearts
            
            AttributeInstance healthAttr = player.getAttribute(Attribute.GENERIC_MAX_HEALTH);
            if (healthAttr != null) {
                double baseHealth = 20.0;
                double newMaxHealth = baseHealth + extraHearts;
                if (healthAttr.getBaseValue() != newMaxHealth) {
                    healthAttr.setBaseValue(newMaxHealth);
                }
            }
        }
    }
    
    private void applyCosmicPassive(Player player) {
        // Permanent resistance 1, speed 1 during night
        player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 100, 0, true, false));
        
        World world = player.getWorld();
        long time = world.getTime();
        if (time > 13000 && time < 23000) { // Night time
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 0, true, false));
        }
    }
    
    private void applyShadowPassive(Player player) {
        // Permanent invisibility
        player.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, 100, 0, true, false));
    }
    
    private void applyLifePassive(Player player) {
        // Permanent 5 extra hearts
        AttributeInstance healthAttr = player.getAttribute(Attribute.GENERIC_MAX_HEALTH);
        if (healthAttr != null && healthAttr.getBaseValue() < 30.0) {
            healthAttr.setBaseValue(30.0); // 20 + 10 = 30 (5 extra hearts)
        }
    }
    
    private void applyDragonPassive(Player player) {
        // Permanent strength 1, speed 1, resistance 1, fire resistance 1
        player.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, 100, 0, true, false));
        player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 0, true, false));
        player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 100, 0, true, false));
        player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 100, 0, true, false));
    }

    // ===== LEVEL 2 ACTIVE ABILITIES =====

    private boolean useBurningActive(Player player) {
        // Passive: When enemy is on fire, do 50% more damage
        player.sendMessage("§6§lBurning Flame Enhanced! Deal 50% more damage to burning enemies!");
        return true; // This is handled in combat events
    }

    private boolean useFrostActive(Player player) {
        // Shoot 3 deadly ice balls that deal 2.5 hearts damage
        player.sendMessage("§b§lFrost Barrage!");

        Location eyeLoc = player.getEyeLocation();
        Vector direction = eyeLoc.getDirection();

        for (int i = 0; i < 3; i++) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    shootIceBall(player, eyeLoc, direction);
                }
            }.runTaskLater(plugin, i * 5L); // Stagger the shots
        }

        return true;
    }

    private boolean useEarthActive(Player player) {
        // Punch into ground and fling blocks, stunning enemies for 8 seconds
        player.sendMessage("§a§lEarth Slam!");

        Location center = player.getLocation();
        World world = center.getWorld();
        if (world == null) return false;

        // Create particle effect
        ParticleUtils.createCircle(center, Particle.BLOCK, 5, 50);
        world.playSound(center, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 0.5f);

        // Stun nearby enemies
        for (Entity entity : world.getNearbyEntities(center, 8, 8, 8)) {
            if (entity instanceof LivingEntity && entity != player) {
                LivingEntity target = (LivingEntity) entity;
                target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 160, 255)); // 8 seconds
                target.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, 160, 128)); // Prevent jumping

                if (target instanceof Player) {
                    ((Player) target).sendMessage("§c§lYou have been stunned by Earth Slam!");
                }
            }
        }

        return true;
    }

    private boolean useAquaticActive(Player player) {
        // Ride a wave toward enemy for 7 seconds, doing 7 hearts and pushing away
        player.sendMessage("§9§lTidal Wave!");

        Location start = player.getLocation();
        Vector direction = player.getEyeLocation().getDirection().setY(0).normalize();

        new BukkitRunnable() {
            int ticks = 0;
            final int maxTicks = 140; // 7 seconds

            @Override
            public void run() {
                if (ticks >= maxTicks || !player.isOnline()) {
                    cancel();
                    return;
                }

                Location currentLoc = start.clone().add(direction.clone().multiply(ticks * 0.3));

                // Create wave particles
                ParticleUtils.createWave(currentLoc, direction, Particle.SPLASH, 3, 2);

                // Move player
                player.teleport(currentLoc);

                // Damage and knockback nearby entities
                World currentWorld = currentLoc.getWorld();
                if (currentWorld != null) {
                    for (Entity entity : currentWorld.getNearbyEntities(currentLoc, 3, 3, 3)) {
                        if (entity instanceof LivingEntity && entity != player) {
                            LivingEntity target = (LivingEntity) entity;
                            target.damage(14.0, player); // 7 hearts

                            Vector knockback = entity.getLocation().toVector()
                                .subtract(currentLoc.toVector()).normalize().multiply(2);
                            entity.setVelocity(knockback);
                        }
                    }
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L);

        return true;
    }

    private boolean useGustActive(Player player) {
        // Shoot into sky and slam down for 6 hearts damage
        player.sendMessage("§f§lSky Slam!");

        Location startLoc = player.getLocation();

        // Launch player up
        player.setVelocity(new Vector(0, 2, 0));

        new BukkitRunnable() {
            int phase = 0; // 0 = going up, 1 = coming down

            @Override
            public void run() {
                if (!player.isOnline()) {
                    cancel();
                    return;
                }

                if (phase == 0 && player.getVelocity().getY() <= 0) {
                    // Start coming down
                    phase = 1;
                    player.setVelocity(new Vector(0, -3, 0));
                } else if (phase == 1 && player.isOnGround()) {
                    // Impact!
                    Location impactLoc = player.getLocation();
                    World world = impactLoc.getWorld();
                    if (world != null) {
                        world.playSound(impactLoc, Sound.ENTITY_GENERIC_EXPLODE, 2.0f, 0.5f);
                        ParticleUtils.createExplosion(impactLoc, Particle.CLOUD, 50);

                        // Damage nearby entities
                        for (Entity entity : world.getNearbyEntities(impactLoc, 5, 5, 5)) {
                            if (entity instanceof LivingEntity && entity != player) {
                                LivingEntity target = (LivingEntity) entity;
                                target.damage(12.0, player); // 6 hearts
                            }
                        }
                    }
                    cancel();
                }
            }
        }.runTaskTimer(plugin, 10L, 1L);

        return true;
    }

    private boolean useCosmicActive(Player player) {
        // Shoot 4 stars doing 2 hearts per hit
        player.sendMessage("§e§lCosmic Barrage!");

        Location eyeLoc = player.getEyeLocation();
        Vector baseDirection = eyeLoc.getDirection();

        for (int i = 0; i < 4; i++) {
            Vector direction = baseDirection.clone();
            // Add slight spread
            direction.add(new Vector(
                (Math.random() - 0.5) * 0.3,
                (Math.random() - 0.5) * 0.3,
                (Math.random() - 0.5) * 0.3
            )).normalize();

            new BukkitRunnable() {
                @Override
                public void run() {
                    shootStar(player, eyeLoc, direction);
                }
            }.runTaskLater(plugin, i * 3L);
        }

        return true;
    }

    private boolean useShadowActive(Player player) {
        // Speed 3, true invisibility, strength 3 for 5 seconds
        player.sendMessage("§8§lShadow Form!");

        player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 2, false, false));
        player.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, 100, 0, false, false));
        player.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, 100, 2, false, false));

        // Hide from all players
        for (Player other : Bukkit.getOnlinePlayers()) {
            if (other != player) {
                other.hidePlayer(plugin, player);
            }
        }

        // Show player again after 5 seconds
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player other : Bukkit.getOnlinePlayers()) {
                    if (other != player) {
                        other.showPlayer(plugin, player);
                    }
                }
                player.sendMessage("§8Shadow form ended.");
            }
        }.runTaskLater(plugin, 100L);

        return true;
    }

    private boolean useLifeActive(Player player) {
        // Create health bubble that heals trusted and damages enemies
        player.sendMessage("§d§lLife Bubble!");

        Location center = player.getLocation();

        new BukkitRunnable() {
            int ticks = 0;
            final int maxTicks = 200; // 10 seconds

            @Override
            public void run() {
                if (ticks >= maxTicks || !player.isOnline()) {
                    cancel();
                    return;
                }

                Location bubbleCenter = player.getLocation();
                World world = bubbleCenter.getWorld();
                if (world == null) {
                    cancel();
                    return;
                }

                // Create bubble particles
                ParticleUtils.createCircle(bubbleCenter.clone().add(0, 1, 0), Particle.HEART, 5, 30);

                // Heal/damage entities in range
                for (Entity entity : world.getNearbyEntities(bubbleCenter, 5, 5, 5)) {
                    if (entity instanceof Player && entity != player) {
                        Player target = (Player) entity;
                        // For now, heal all players (you could add a trust system later)
                        double newHealth = Math.min(target.getMaxHealth(), target.getHealth() + 1);
                        target.setHealth(newHealth);
                        target.sendMessage("§d§lHealed by Life Bubble!");
                    } else if (entity instanceof Monster) {
                        Monster monster = (Monster) entity;
                        monster.damage(2.0, player);
                    }
                }

                ticks += 10; // Run every 10 ticks (0.5 seconds)
            }
        }.runTaskTimer(plugin, 0L, 10L);

        return true;
    }

    private boolean useDragonActive(Player player) {
        // Summon dragon or create ender ring
        player.sendMessage("§5§lDragon Power!");

        // For now, create the ender ring (dragon summoning is complex)
        createEnderRing(player);

        return true;
    }

    // ===== HELPER METHODS =====

    private void shootIceBall(Player shooter, Location start, Vector direction) {
        World world = start.getWorld();
        if (world == null) return;

        Snowball iceBall = world.spawn(start, Snowball.class);
        iceBall.setShooter(shooter);
        iceBall.setVelocity(direction.multiply(2));

        new BukkitRunnable() {
            @Override
            public void run() {
                if (iceBall.isDead() || !iceBall.isValid()) {
                    cancel();
                    return;
                }

                Location loc = iceBall.getLocation();
                world.spawnParticle(Particle.SNOWFLAKE, loc, 5, 0.2, 0.2, 0.2, 0);

                // Check for nearby entities to damage
                for (Entity entity : world.getNearbyEntities(loc, 1.5, 1.5, 1.5)) {
                    if (entity instanceof LivingEntity && entity != shooter) {
                        LivingEntity target = (LivingEntity) entity;
                        target.damage(5.0, shooter); // 2.5 hearts
                        target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 1, false, false));

                        // Create ice explosion
                        world.spawnParticle(Particle.BLOCK, loc, 20, 1, 1, 1, 0);
                        world.playSound(loc, Sound.BLOCK_GLASS_BREAK, 1.0f, 1.5f);

                        iceBall.remove();
                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }

    private void shootStar(Player shooter, Location start, Vector direction) {
        World world = start.getWorld();
        if (world == null) return;

        // Create a moving star projectile
        new BukkitRunnable() {
            Location currentLoc = start.clone();
            int ticks = 0;
            final int maxTicks = 60; // 3 seconds max travel time

            @Override
            public void run() {
                if (ticks >= maxTicks) {
                    cancel();
                    return;
                }

                currentLoc.add(direction.clone().multiply(0.5));

                // Create star particles
                world.spawnParticle(Particle.END_ROD, currentLoc, 3, 0.1, 0.1, 0.1, 0);

                // Check for hits
                for (Entity entity : world.getNearbyEntities(currentLoc, 1, 1, 1)) {
                    if (entity instanceof LivingEntity && entity != shooter) {
                        LivingEntity target = (LivingEntity) entity;
                        target.damage(4.0, shooter); // 2 hearts

                        // Star impact effect
                        ParticleUtils.createStar(currentLoc, Particle.END_ROD, 2);
                        world.playSound(currentLoc, Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 2.0f);

                        cancel();
                        return;
                    }
                }

                // Check for block collision
                if (currentLoc.getBlock().getType().isSolid()) {
                    ParticleUtils.createStar(currentLoc, Particle.END_ROD, 2);
                    cancel();
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }

    private void createEnderRing(Player player) {
        Location center = player.getLocation();
        World world = center.getWorld();
        if (world == null) return;

        world.playSound(center, Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 1.0f);

        new BukkitRunnable() {
            int ticks = 0;
            final int maxTicks = 300; // 15 seconds

            @Override
            public void run() {
                if (ticks >= maxTicks || !player.isOnline()) {
                    cancel();
                    return;
                }

                Location ringCenter = player.getLocation();

                // Create ender ring particles
                ParticleUtils.createCircle(ringCenter, Particle.PORTAL, 10, 50);
                ParticleUtils.createCircle(ringCenter.clone().add(0, 1, 0), Particle.PORTAL, 10, 50);

                // Damage and debuff entities in ring
                for (Entity entity : world.getNearbyEntities(ringCenter, 10, 10, 10)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;
                        target.damage(3.0, player); // 1.5 hearts per second
                        target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 40, 0, false, false));
                        target.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 40, 0, false, false));
                    }
                }

                ticks += 20; // Run every second
            }
        }.runTaskTimer(plugin, 0L, 20L);
    }

    /**
     * Handle special combat effects for flame abilities
     */
    public void handleCombatEffect(Player attacker, LivingEntity target, double damage) {
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(attacker);
        if (!flamePlayer.hasActiveFlame()) return;

        FlameType flameType = flamePlayer.getFlameType();

        switch (flameType) {
            case BURNING:
                // Level 2: 50% more damage to burning enemies
                if (flamePlayer.getUpgradeLevel() >= 2 && target.getFireTicks() > 0) {
                    target.damage(damage * 0.5, attacker);
                    attacker.sendMessage("§6§lBurning bonus damage!");
                }
                break;

            case FROST:
                // Level 1: 30% chance to freeze on hit
                if (random.nextInt(100) < 30) {
                    target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 2, false, false));
                    if (target instanceof Player) {
                        Player targetPlayer = (Player) target;
                        targetPlayer.sendMessage("§b§lYou have been frozen!");
                    }
                    ParticleUtils.createCircle(target.getLocation(), Particle.SNOWFLAKE, 2, 20);
                }
                break;

            case SHADOW:
                // Level 1: 40% chance to blind nearby players when crouching
                if (attacker.isSneaking() && random.nextInt(100) < 40) {
                    for (Entity entity : attacker.getWorld().getNearbyEntities(attacker.getLocation(), 5, 5, 5)) {
                        if (entity instanceof Player && entity != attacker) {
                            Player nearbyPlayer = (Player) entity;
                            nearbyPlayer.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 60, 0, false, false));
                            nearbyPlayer.sendMessage("§8§lBlinded by shadow!");
                        }
                    }
                }
                break;
        }
    }

    /**
     * Handle special effects when player eats golden apple (Life Flame)
     */
    public void handleGoldenAppleEat(Player player) {
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(player);
        if (flamePlayer.hasActiveFlame() && flamePlayer.getFlameType() == FlameType.LIFE) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 100, 2));
            player.sendMessage("§d§lLife Flame activated! Enhanced regeneration!");
        }
    }
}
