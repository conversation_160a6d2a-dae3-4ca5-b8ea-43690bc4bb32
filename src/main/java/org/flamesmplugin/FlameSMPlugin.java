package org.flamesmplugin;

import org.bukkit.plugin.java.JavaPlugin;
import org.flamesmplugin.commands.FlameCommand;
import org.flamesmplugin.listeners.CombatEventListener;
import org.flamesmplugin.listeners.PlayerEventListener;

public final class FlameSMPlugin extends JavaPlugin {
    
    private FlameManager flameManager;
    private CooldownManager cooldownManager;
    private FlameAbilities flameAbilities;
    private FlameItems flameItems;

    @Override
    public void onEnable() {
        getLogger().info("FlameSMPlugin is starting up...");
        
        // Initialize managers
        flameManager = new FlameManager(this);
        cooldownManager = new CooldownManager();
        flameAbilities = new FlameAbilities(this, flameManager, cooldownManager);
        flameItems = new FlameItems(this);
        
        // Register event listeners
        getServer().getPluginManager().registerEvents(
            new PlayerEventListener(this, flameManager, flameAbilities, flameItems), this);
        getServer().getPluginManager().registerEvents(
            new CombatEventListener(flameManager, flameAbilities), this);
        
        // Register commands
        FlameCommand flameCommand = new FlameCommand(flameManager, flameItems);
        getCommand("flame").setExecutor(flameCommand);
        getCommand("flame").setTabCompleter(flameCommand);
        
        getLogger().info("FlameSMPlugin has been enabled successfully!");
    }

    @Override
    public void onDisable() {
        getLogger().info("FlameSMPlugin is shutting down...");
        
        // Save all player data
        if (flameManager != null) {
            flameManager.saveAllData();
        }
        
        getLogger().info("FlameSMPlugin has been disabled successfully!");
    }
    
    // Getters for other classes to access managers
    public FlameManager getFlameManager() {
        return flameManager;
    }
    
    public CooldownManager getCooldownManager() {
        return cooldownManager;
    }
    
    public FlameAbilities getFlameAbilities() {
        return flameAbilities;
    }
    
    public FlameItems getFlameItems() {
        return flameItems;
    }
}
