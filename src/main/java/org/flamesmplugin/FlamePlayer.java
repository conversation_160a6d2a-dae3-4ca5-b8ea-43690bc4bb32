package org.flamesmplugin;

import org.bukkit.entity.Player;
import java.util.UUID;

/**
 * Represents a player's flame data
 */
public class FlamePlayer {
    private final UUID playerId;
    private FlameType flameType;
    private int upgradeLevel; // 1 or 2
    private int deaths;
    private boolean flameExtinguished;
    private long lastAbilityUse;
    
    public FlamePlayer(UUID playerId) {
        this.playerId = playerId;
        this.flameType = null;
        this.upgradeLevel = 1;
        this.deaths = 0;
        this.flameExtinguished = false;
        this.lastAbilityUse = 0;
    }
    
    public FlamePlayer(UUID playerId, FlameType flameType, int upgradeLevel, int deaths, boolean flameExtinguished) {
        this.playerId = playerId;
        this.flameType = flameType;
        this.upgradeLevel = upgradeLevel;
        this.deaths = deaths;
        this.flameExtinguished = flameExtinguished;
        this.lastAbilityUse = 0;
    }
    
    // Getters and setters
    public UUID getPlayerId() {
        return playerId;
    }
    
    public FlameType getFlameType() {
        return flameType;
    }
    
    public void setFlameType(FlameType flameType) {
        this.flameType = flameType;
    }
    
    public int getUpgradeLevel() {
        return upgradeLevel;
    }
    
    public void setUpgradeLevel(int upgradeLevel) {
        this.upgradeLevel = Math.max(1, Math.min(2, upgradeLevel));
    }
    
    public int getDeaths() {
        return deaths;
    }
    
    public void addDeath() {
        this.deaths++;
        if (this.deaths >= 3) {
            this.flameExtinguished = true;
        }
    }
    
    public void resetDeaths() {
        this.deaths = 0;
    }
    
    public boolean isFlameExtinguished() {
        return flameExtinguished;
    }
    
    public void setFlameExtinguished(boolean flameExtinguished) {
        this.flameExtinguished = flameExtinguished;
    }
    
    public void relightFlame() {
        this.flameExtinguished = false;
        this.deaths = 0;
    }
    
    public long getLastAbilityUse() {
        return lastAbilityUse;
    }
    
    public void setLastAbilityUse(long lastAbilityUse) {
        this.lastAbilityUse = lastAbilityUse;
    }
    
    /**
     * Check if the player has a flame and it's not extinguished
     */
    public boolean hasActiveFlame() {
        return flameType != null && !flameExtinguished;
    }
    
    /**
     * Check if the player can use level 2 abilities
     */
    public boolean canUseLevel2Abilities() {
        return hasActiveFlame() && upgradeLevel >= 2;
    }
    
    /**
     * Get the flame's current "health" (3 - deaths)
     */
    public int getFlameHealth() {
        return Math.max(0, 3 - deaths);
    }
}
