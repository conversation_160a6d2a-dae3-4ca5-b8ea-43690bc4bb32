package org.flamesmplugin;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

/**
 * Utility class for creating particle effects
 */
public class ParticleUtils {
    
    /**
     * Create a circle of particles around a location
     */
    public static void createCircle(Location center, Particle particle, double radius, int points) {
        World world = center.getWorld();
        if (world == null) return;
        
        for (int i = 0; i < points; i++) {
            double angle = 2 * Math.PI * i / points;
            double x = center.getX() + radius * Math.cos(angle);
            double z = center.getZ() + radius * Math.sin(angle);
            Location particleLoc = new Location(world, x, center.getY(), z);
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }
    
    /**
     * Create a spiral of particles
     */
    public static void createSpiral(Location center, Particle particle, double height, double radius) {
        World world = center.getWorld();
        if (world == null) return;
        
        for (double y = 0; y <= height; y += 0.2) {
            double angle = y * 4; // Controls spiral tightness
            double x = center.getX() + radius * Math.cos(angle);
            double z = center.getZ() + radius * Math.sin(angle);
            Location particleLoc = new Location(world, x, center.getY() + y, z);
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }
    
    /**
     * Create a line of particles between two points
     */
    public static void createLine(Location start, Location end, Particle particle, double spacing) {
        World world = start.getWorld();
        if (world == null || !world.equals(end.getWorld())) return;
        
        Vector direction = end.toVector().subtract(start.toVector());
        double distance = direction.length();
        direction.normalize();
        
        for (double d = 0; d <= distance; d += spacing) {
            Location particleLoc = start.clone().add(direction.clone().multiply(d));
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }
    
    /**
     * Create an explosion effect with particles
     */
    public static void createExplosion(Location center, Particle particle, int particleCount) {
        World world = center.getWorld();
        if (world == null) return;
        
        for (int i = 0; i < particleCount; i++) {
            Vector randomDirection = new Vector(
                Math.random() - 0.5,
                Math.random() - 0.5,
                Math.random() - 0.5
            ).normalize().multiply(Math.random() * 2);
            
            Location particleLoc = center.clone().add(randomDirection);
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }
    
    /**
     * Create a wave effect moving in a direction
     */
    public static void createWave(Location start, Vector direction, Particle particle, double length, double width) {
        World world = start.getWorld();
        if (world == null) return;
        
        Vector perpendicular = new Vector(-direction.getZ(), 0, direction.getX()).normalize();
        
        for (double d = 0; d <= length; d += 0.5) {
            for (double w = -width/2; w <= width/2; w += 0.3) {
                Location particleLoc = start.clone()
                    .add(direction.clone().multiply(d))
                    .add(perpendicular.clone().multiply(w));
                world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
            }
        }
    }
    
    /**
     * Create star-shaped particle effect
     */
    public static void createStar(Location center, Particle particle, double size) {
        World world = center.getWorld();
        if (world == null) return;
        
        // Create 5-pointed star
        for (int i = 0; i < 5; i++) {
            double angle1 = Math.toRadians(i * 72);
            double angle2 = Math.toRadians((i + 1) * 72);
            
            // Outer points
            Location point1 = center.clone().add(
                size * Math.cos(angle1), 0, size * Math.sin(angle1)
            );
            Location point2 = center.clone().add(
                size * Math.cos(angle2), 0, size * Math.sin(angle2)
            );
            
            // Inner points
            double innerAngle = Math.toRadians(i * 72 + 36);
            Location innerPoint = center.clone().add(
                size * 0.4 * Math.cos(innerAngle), 0, size * 0.4 * Math.sin(innerAngle)
            );
            
            // Draw lines
            createLine(point1, innerPoint, particle, 0.2);
            createLine(innerPoint, point2, particle, 0.2);
        }
    }
    
    /**
     * Create ambient particles around a player based on their flame type
     */
    public static void createAmbientEffect(Player player, FlameType flameType) {
        if (flameType == null) return;
        
        Location loc = player.getLocation().add(0, 1, 0);
        Particle particle = flameType.getParticle();
        
        // Create subtle ambient effect
        for (int i = 0; i < 3; i++) {
            Vector offset = new Vector(
                (Math.random() - 0.5) * 2,
                Math.random() * 2,
                (Math.random() - 0.5) * 2
            );
            loc.getWorld().spawnParticle(particle, loc.clone().add(offset), 1, 0, 0, 0, 0);
        }
    }
}
