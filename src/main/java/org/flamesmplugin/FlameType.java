package org.flamesmplugin;

import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.potion.PotionEffectType;

/**
 * Enum representing all flame types with their properties
 */
public enum FlameType {
    BURNING("🔥 Burning Flame", Material.FIRE_CHARGE, Particle.FLAME, 
            "Permanent fire resistance 1, strength 1 while on fire"),
    
    FROST("❄️ Frost Flame", Material.ICE, Particle.SNOWFLAKE,
          "Immune to freezing, regeneration 2 while in snow, 30% chance of freezing on hit"),
    
    EARTH("🪨 Earth Flame", Material.STONE, Particle.BLOCK,
          "Double ore drops, permanent haste 1, auto enchant efficiency 2"),
    
    AQUATIC("🌊 Aquatic Flame", Material.WATER_BUCKET, Particle.SPLASH,
            "Auto enchant aqua affinity, turtle helmet breathing, dolphins grace 1"),
    
    GUST("💨 Gust Flame", Material.FEATHER, Particle.CLOUD,
         "Permanent speed 1, extra hearts above Y70, immune to fall damage"),
    
    COSMIC("⭐️ Cosmic Flame", Material.NETHER_STAR, Particle.END_ROD,
           "Permanent resistance 1, auto enchant protection 1, speed 1 at night"),
    
    SHADOW("💀 Shadow Flame", Material.WITHER_SKELETON_SKULL, Particle.CAMPFIRE_SIGNAL_SMOKE,
           "Permanent invisibility, 40% chance to blind nearby players when crouching"),
    
    LIFE("💕 Life Flame", Material.GOLDEN_APPLE, Particle.HEART,
         "Permanent 5 extra hearts, regeneration 3 for 5s when eating golden apple"),
    
    DRAGON("🐉 Dragon Flame", Material.DRAGON_HEAD, Particle.DRAGON_BREATH,
           "Permanent strength 1, speed 1, resistance 1, fire resistance 1, immune to hostile mobs");

    private final String displayName;
    private final Material iconMaterial;
    private final Particle particle;
    private final String description;

    FlameType(String displayName, Material iconMaterial, Particle particle, String description) {
        this.displayName = displayName;
        this.iconMaterial = iconMaterial;
        this.particle = particle;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public Material getIconMaterial() {
        return iconMaterial;
    }

    public Particle getParticle() {
        return particle;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Get a random flame type (excluding Dragon)
     */
    public static FlameType getRandomFlame() {
        FlameType[] values = values();
        FlameType[] nonDragon = new FlameType[values.length - 1];
        System.arraycopy(values, 0, nonDragon, 0, values.length - 1);
        return nonDragon[(int) (Math.random() * nonDragon.length)];
    }

    /**
     * Check if this flame type can be randomly assigned to new players
     */
    public boolean isRandomlyAssignable() {
        return this != DRAGON;
    }
}
