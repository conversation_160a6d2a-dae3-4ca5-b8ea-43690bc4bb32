package org.flamesmplugin.listeners;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.*;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.flamesmplugin.*;

import java.util.Random;

/**
 * Handles player-related events
 */
public class PlayerEventListener implements Listener {
    private final FlameSMPlugin plugin;
    private final FlameManager flameManager;
    private final FlameAbilities flameAbilities;
    private final FlameItems flameItems;
    private final Random random;
    
    public PlayerEventListener(FlameSMPlugin plugin, FlameManager flameManager, 
                              FlameAbilities flameAbilities, FlameItems flameItems) {
        this.plugin = plugin;
        this.flameManager = flameManager;
        this.flameAbilities = flameAbilities;
        this.flameItems = flameItems;
        this.random = new Random();
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Assign random flame if new player
        if (!player.hasPlayedBefore()) {
            flameManager.assignRandomFlame(player);
        }
        
        // Start passive ability task
        startPassiveAbilityTask(player);
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Save player data when they leave
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(event.getPlayer());
        flameManager.savePlayerData(flamePlayer);
    }
    
    @EventHandler
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        flameManager.handlePlayerDeath(player);
    }
    
    @EventHandler
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        Player player = event.getPlayer();
        
        // Restart passive abilities after respawn
        new BukkitRunnable() {
            @Override
            public void run() {
                startPassiveAbilityTask(player);
            }
        }.runTaskLater(plugin, 20L); // Wait 1 second after respawn
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item == null) return;
        
        // Handle custom items
        if (flameItems.isFlameMatch(item)) {
            handleFlameMatchUse(player, item);
            event.setCancelled(true);
        } else if (flameItems.isFlameUpgrader(item)) {
            handleFlameUpgraderUse(player, item);
            event.setCancelled(true);
        } else if (flameItems.isFlameMace(item)) {
            handleFlameMaceUse(player);
        }
        
        // Handle Level 2 ability activation (right-click with any item)
        if (event.getAction().name().contains("RIGHT_CLICK")) {
            FlamePlayer flamePlayer = flameManager.getFlamePlayer(player);
            if (flamePlayer.canUseLevel2Abilities() && player.isSneaking()) {
                flameAbilities.useActiveAbility(player);
            }
        }
    }
    
    @EventHandler
    public void onPlayerConsume(PlayerItemConsumeEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        // Handle golden apple consumption for Life Flame
        if (item.getType() == Material.GOLDEN_APPLE || item.getType() == Material.ENCHANTED_GOLDEN_APPLE) {
            flameAbilities.handleGoldenAppleEat(player);
        }
    }
    
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(player);
        
        if (!flamePlayer.hasActiveFlame()) return;
        
        // Handle Earth Flame double ore drops
        if (flamePlayer.getFlameType() == FlameType.EARTH) {
            Material blockType = event.getBlock().getType();
            if (isOre(blockType)) {
                // Double the drops
                for (ItemStack drop : event.getBlock().getDrops(player.getInventory().getItemInMainHand())) {
                    event.getBlock().getWorld().dropItemNaturally(event.getBlock().getLocation(), drop);
                }
                player.sendMessage("§a§lEarth Flame: Double ore drops!");
            }
        }
        
        // Auto-enchant tools
        ItemStack tool = player.getInventory().getItemInMainHand();
        if (tool != null && tool.getType() != Material.AIR) {
            flameItems.autoEnchantTool(tool, flamePlayer.getFlameType());
        }
    }
    
    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(player);
        
        if (!flamePlayer.hasActiveFlame()) return;
        
        // Handle Gust Flame fall damage immunity
        if (flamePlayer.getFlameType() == FlameType.GUST) {
            if (player.getFallDistance() > 0) {
                player.setFallDistance(0);
            }
        }
    }
    
    private void startPassiveAbilityTask(Player player) {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!player.isOnline()) {
                    cancel();
                    return;
                }
                
                flameAbilities.applyPassiveAbilities(player);
            }
        }.runTaskTimer(plugin, 0L, 20L); // Run every second
    }
    
    private void handleFlameMatchUse(Player player, ItemStack match) {
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(player);
        
        if (flamePlayer.getFlameType() == null) {
            player.sendMessage("§cYou don't have a flame to relight!");
            return;
        }
        
        if (!flamePlayer.isFlameExtinguished()) {
            player.sendMessage("§cYour flame is not extinguished!");
            return;
        }
        
        flameManager.relightFlame(player);
        
        // Consume the match
        if (match.getAmount() > 1) {
            match.setAmount(match.getAmount() - 1);
        } else {
            player.getInventory().setItemInMainHand(null);
        }
        
        player.sendMessage("§a§lYour flame has been relit with the Flame Match!");
    }
    
    private void handleFlameUpgraderUse(Player player, ItemStack upgrader) {
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(player);
        
        if (!flamePlayer.hasActiveFlame()) {
            player.sendMessage("§cYou need an active flame to upgrade!");
            return;
        }
        
        if (flamePlayer.getUpgradeLevel() >= 2) {
            player.sendMessage("§cYour flame is already at maximum level!");
            return;
        }
        
        flameManager.upgradeFlame(player);
        
        // Consume the upgrader
        if (upgrader.getAmount() > 1) {
            upgrader.setAmount(upgrader.getAmount() - 1);
        } else {
            player.getInventory().setItemInMainHand(null);
        }
    }
    
    private void handleFlameMaceUse(Player player) {
        // Dash ability for flame mace
        player.sendMessage("§c§lFlame Mace Dash!");
        
        // Launch player forward
        player.setVelocity(player.getLocation().getDirection().multiply(2).setY(0.5));
        
        // Break shields in 30 block radius
        for (Player nearbyPlayer : player.getWorld().getPlayers()) {
            if (nearbyPlayer != player && nearbyPlayer.getLocation().distance(player.getLocation()) <= 30) {
                ItemStack offhand = nearbyPlayer.getInventory().getItemInOffHand();
                if (offhand != null && offhand.getType() == Material.SHIELD) {
                    nearbyPlayer.getInventory().setItemInOffHand(null);
                    nearbyPlayer.sendMessage("§c§lYour shield was shattered by the Flame Mace!");
                }
            }
        }
    }
    
    private boolean isOre(Material material) {
        String name = material.name();
        return name.contains("_ORE") || name.equals("ANCIENT_DEBRIS");
    }
}
