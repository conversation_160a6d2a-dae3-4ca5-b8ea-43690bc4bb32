package org.flamesmplugin;

import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages ability cooldowns for players
 */
public class CooldownManager {
    private final Map<UUID, Long> cooldowns;
    
    public CooldownManager() {
        this.cooldowns = new HashMap<>();
    }
    
    /**
     * Set a cooldown for a player
     */
    public void setCooldown(Player player, int seconds) {
        setCooldown(player.getUniqueId(), seconds);
    }
    
    public void setCooldown(UUID playerId, int seconds) {
        long cooldownTime = System.currentTimeMillis() + (seconds * 1000L);
        cooldowns.put(playerId, cooldownTime);
    }
    
    /**
     * Check if a player is on cooldown
     */
    public boolean isOnCooldown(Player player) {
        return isOnCooldown(player.getUniqueId());
    }
    
    public boolean isOnCooldown(UUID playerId) {
        Long cooldownTime = cooldowns.get(playerId);
        if (cooldownTime == null) return false;
        
        if (System.currentTimeMillis() >= cooldownTime) {
            cooldowns.remove(playerId);
            return false;
        }
        return true;
    }
    
    /**
     * Get remaining cooldown time in seconds
     */
    public int getRemainingCooldown(Player player) {
        return getRemainingCooldown(player.getUniqueId());
    }
    
    public int getRemainingCooldown(UUID playerId) {
        Long cooldownTime = cooldowns.get(playerId);
        if (cooldownTime == null) return 0;
        
        long remaining = cooldownTime - System.currentTimeMillis();
        if (remaining <= 0) {
            cooldowns.remove(playerId);
            return 0;
        }
        
        return (int) Math.ceil(remaining / 1000.0);
    }
    
    /**
     * Remove cooldown for a player
     */
    public void removeCooldown(Player player) {
        removeCooldown(player.getUniqueId());
    }
    
    public void removeCooldown(UUID playerId) {
        cooldowns.remove(playerId);
    }
    
    /**
     * Get cooldown duration for each flame type's level 2 ability
     */
    public static int getCooldownForFlame(FlameType flameType) {
        switch (flameType) {
            case FROST:
            case AQUATIC:
            case SHADOW:
                return 90; // 90 seconds
            case GUST:
            case COSMIC:
                return 75; // 75 seconds
            case EARTH:
            case DRAGON:
                return 60; // 60 seconds
            case LIFE:
                return 60; // 1 minute
            case BURNING:
                return 0; // No cooldown, passive ability
            default:
                return 60;
        }
    }
}
