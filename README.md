# FlameSMPlugin

A comprehensive flame-based ability system for Minecraft 1.21 Paper/Spigot servers.

## Features

### 8 Unique Flame Types

1. **🔥 Burning Flame**
   - Level 1: Permanent fire resistance 1, strength 1 while on fire
   - Level 2: 50% more damage to burning enemies

2. **❄️ Frost Flame**
   - Level 1: Immune to freezing, regeneration 2 while in snow, 30% chance to freeze on hit
   - Level 2: Shoot 3 ice balls dealing 2.5 hearts each (90s cooldown)

3. **🪨 Earth Flame**
   - Level 1: Double ore drops, permanent haste 1, auto-enchant efficiency 2
   - Level 2: Ground slam stunning enemies for 8 seconds (60s cooldown)

4. **🌊 Aquatic Flame**
   - Level 1: Auto-enchant aqua affinity, turtle helmet breathing, dolphins grace 1
   - Level 2: Ride a wave dealing 7 hearts damage (90s cooldown)

5. **💨 Gust Flame**
   - Level 1: Permanent speed 1, extra hearts above Y70, immune to fall damage
   - Level 2: Sky slam dealing 6 hearts damage (75s cooldown)

6. **⭐️ Cosmic Flame**
   - Level 1: Permanent resistance 1, auto-enchant protection 1, speed 1 at night
   - Level 2: Shoot 4 stars dealing 2 hearts each (75s cooldown)

7. **💀 Shadow Flame**
   - Level 1: Permanent invisibility, 40% chance to blind nearby players when crouching
   - Level 2: True invisibility + speed 3 + strength 3 for 5 seconds (90s cooldown)

8. **💕 Life Flame**
   - Level 1: Permanent 5 extra hearts, regeneration 3 when eating golden apples
   - Level 2: Create healing bubble for allies, damaging enemies (60s cooldown)

9. **🐉 Dragon Flame** (Boss reward only)
   - Level 1: Permanent strength 1, speed 1, resistance 1, fire resistance 1, immune to hostile mobs
   - Level 2: Summon ender ring dealing damage and debuffs (60s cooldown)

### Life System
- Players start with 3 flame lives
- Each death damages the flame
- After 3 deaths, the flame is extinguished
- Use a Flame Match to relight extinguished flames

### Custom Items

#### Flame Match
- Relights extinguished flames
- Crafted or given by admins

#### Flame Mace
- Custom weapon with dash ability
- Breaks all shields within 30 blocks
- Obtained through boss fights

#### Flame Upgrader
- Upgrades flames from Level 1 to Level 2
- Unlocks powerful active abilities

## Commands

### Player Commands
- `/flame info [player]` - Show flame information
- `/flame list` - List all flame types

### Admin Commands
- `/flame set <player> <type>` - Set player's flame type
- `/flame upgrade [player]` - Upgrade flame to Level 2
- `/flame relight [player]` - Relight extinguished flame
- `/flame give <player> <item>` - Give custom items (match, mace, upgrader)
- `/flame reset <player>` - Reset player's flame data

## Permissions
- `flame.use` - Basic flame commands (default: true)
- `flame.admin` - Administrative commands (default: op)

## Installation

1. Download the plugin JAR file
2. Place it in your server's `plugins` folder
3. Restart your server
4. Configure the plugin in `plugins/FlameSMPlugin/config.yml`

## Configuration

The plugin creates a `config.yml` file with various settings:
- Cooldown times for abilities
- Combat settings (damage bonuses, chances)
- Message customization
- Feature toggles

## How to Use

### For Players
1. New players automatically receive a random flame (except Dragon)
2. Use passive abilities automatically
3. Sneak + right-click to use Level 2 abilities (requires upgrade)
4. Avoid dying 3 times or your flame will be extinguished!

### For Admins
1. Use `/flame give <player> upgrader` to give upgrade items
2. Use `/flame give <player> match` to give relight items
3. Use `/flame set <player> dragon` to give the special Dragon flame
4. Monitor player flames with `/flame info <player>`

## Building from Source

Requirements:
- Java 21
- Maven 3.6+

```bash
git clone <repository>
cd FlameSMPlugin
mvn clean package
```

The compiled JAR will be in the `target` folder.

## Support

This plugin is designed for Minecraft 1.21 Paper/Spigot servers. For issues or feature requests, please contact the developer.

## Version History

- v1.0-SNAPSHOT: Initial release with all 9 flame types and core features
